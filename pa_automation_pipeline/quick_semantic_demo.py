#!/usr/bin/env python3
"""
Quick Semantic Demo - Creates a filled PDF using your existing successful data
with semantic enhancements applied.
"""

import json
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def create_semantic_enhanced_pdf():
    """Create a semantic enhanced PDF using your existing successful data."""
    
    print("🧠 CREATING SEMANTIC ENHANCED PDF")
    print("=" * 50)
    
    # Load your existing successful data
    extracted_data_path = Path("fixed_output/Akshay_fixed_extracted.json")
    pa_schema_path = Path("fixed_output/Akshay_actual_fields.json")
    
    if not extracted_data_path.exists():
        print(f"❌ Extracted data not found: {extracted_data_path}")
        return False
    
    if not pa_schema_path.exists():
        print(f"❌ PA schema not found: {pa_schema_path}")
        return False
    
    # Load the data
    print("📂 Loading existing successful data...")
    with open(extracted_data_path, 'r') as f:
        extraction_results = json.load(f)
    
    with open(pa_schema_path, 'r') as f:
        pa_schema_data = json.load(f)
    
    extracted_data = extraction_results["extracted_data"]
    pa_schema = pa_schema_data["fields"]
    
    print(f"✅ Loaded {len(extracted_data)} extracted fields")
    print(f"✅ Loaded {len(pa_schema)} PA form fields")
    
    # Show original data
    print(f"\n📊 ORIGINAL EXTRACTED DATA:")
    for field_id, value in extracted_data.items():
        if value is not None:
            display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
            print(f"   {field_id}: {display_value}")
    
    # Apply semantic enhancements
    print(f"\n🧠 APPLYING SEMANTIC ENHANCEMENTS...")
    enhanced_data = apply_semantic_enhancements(extracted_data)
    
    # Create enhanced schema for form filling
    enhanced_schema = {}
    for field_id, field_info in pa_schema.items():
        enhanced_schema[field_id] = {
            "acro_id": field_id,
            "semantic_meaning": field_info.get("field_name", field_id),
            "type": field_info.get("field_type", "text"),
            "human_name": field_info.get("field_name", field_id)
        }
    
    # Create output directory
    output_dir = Path("semantic_demo_output")
    output_dir.mkdir(exist_ok=True)
    
    # Fill the PDF
    print(f"\n📝 FILLING PDF WITH ENHANCED DATA...")
    
    try:
        from src.form_filler import fill_pa_form
        
        pa_form_path = Path("Input Data/Akshay/pa.pdf")
        filled_pdf_path = output_dir / "SEMANTIC_ENHANCED_Akshay_PA.pdf"
        
        success = fill_pa_form(
            blank_pdf_path=pa_form_path,
            schema=enhanced_schema,
            extracted_data=enhanced_data,
            output_path=filled_pdf_path
        )
        
        if success:
            print(f"✅ SUCCESS! Semantic enhanced PDF created:")
            print(f"   📄 {filled_pdf_path}")
            
            # Save the enhanced data for comparison
            results_path = output_dir / "enhanced_data_comparison.json"
            with open(results_path, 'w') as f:
                json.dump({
                    "original_data": extracted_data,
                    "enhanced_data": enhanced_data,
                    "enhancements_applied": get_enhancement_summary(extracted_data, enhanced_data)
                }, f, indent=2)
            
            print(f"   💾 {results_path}")
            
            # Create a simple report
            create_simple_report(extracted_data, enhanced_data, output_dir)
            
            return True
        else:
            print(f"❌ PDF filling failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during PDF filling: {e}")
        return False

def apply_semantic_enhancements(data):
    """Apply semantic enhancements to the data."""
    
    enhanced = {}
    transformations = []
    
    for field_id, value in data.items():
        if value is None:
            enhanced[field_id] = None
            continue
        
        # Apply enhancements based on field patterns
        enhanced_value, transformation = enhance_field_value(field_id, value)
        enhanced[field_id] = enhanced_value
        
        if transformation:
            transformations.append({
                "field": field_id,
                "original": value,
                "enhanced": enhanced_value,
                "transformation": transformation
            })
    
    if transformations:
        print(f"🔄 Applied {len(transformations)} enhancements:")
        for t in transformations:
            print(f"   {t['field']}: {t['transformation']}")
    else:
        print(f"✅ Data already well-formatted - no enhancements needed!")
    
    return enhanced

def enhance_field_value(field_id, value):
    """Enhance a single field value based on semantic understanding."""
    
    if value is None:
        return None, None
    
    value_str = str(value).strip()
    
    # Phone number enhancement
    if "phone" in field_id.lower() or field_id == "Phone T":
        import re
        digits = re.sub(r'\D', '', value_str)
        if len(digits) == 10:
            formatted = f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
            if formatted != value_str:
                return formatted, "phone_formatted"
    
    # Date enhancement
    if field_id == "T13":  # Date of birth field
        import re
        if re.match(r'^\d{2}/\d{2}/\d{4}$', value_str):
            return value_str, None  # Already formatted
        elif re.match(r'^\d{4}-\d{2}-\d{2}$', value_str):
            parts = value_str.split('-')
            formatted = f"{parts[1]}/{parts[2]}/{parts[0]}"
            return formatted, "date_reformatted"
    
    # Name enhancement
    if field_id in ["T11", "T12"]:  # Name fields
        if value_str.lower() == value_str or value_str.upper() == value_str:
            formatted = value_str.title()
            if formatted != value_str:
                return formatted, "name_title_cased"
    
    # Clean whitespace
    cleaned = ' '.join(value_str.split())
    if cleaned != value_str:
        return cleaned, "whitespace_cleaned"
    
    return value_str, None

def get_enhancement_summary(original, enhanced):
    """Get summary of enhancements applied."""
    
    changes = []
    for field_id in original:
        if original[field_id] != enhanced[field_id]:
            changes.append({
                "field": field_id,
                "original": original[field_id],
                "enhanced": enhanced[field_id]
            })
    
    return changes

def create_simple_report(original_data, enhanced_data, output_dir):
    """Create a simple comparison report."""
    
    report_path = output_dir / "SEMANTIC_ENHANCEMENT_REPORT.md"
    
    with open(report_path, 'w') as f:
        f.write("# Semantic Enhancement Report\n\n")
        f.write("## Summary\n\n")
        f.write(f"- **Total Fields**: {len(enhanced_data)}\n")
        f.write(f"- **Non-null Fields**: {sum(1 for v in enhanced_data.values() if v is not None)}\n")
        
        # Find changes
        changes = get_enhancement_summary(original_data, enhanced_data)
        f.write(f"- **Enhancements Applied**: {len(changes)}\n\n")
        
        if changes:
            f.write("## Enhancements Applied\n\n")
            for i, change in enumerate(changes, 1):
                f.write(f"### {i}. {change['field']}\n\n")
                f.write(f"**Original:** `{change['original']}`\n\n")
                f.write(f"**Enhanced:** `{change['enhanced']}`\n\n")
                f.write("---\n\n")
        else:
            f.write("## No Enhancements Needed\n\n")
            f.write("Your data is already well-formatted!\n\n")
        
        f.write("## All Field Values\n\n")
        for field_id, value in enhanced_data.items():
            if value is not None:
                f.write(f"- **{field_id}**: {value}\n")
    
    print(f"   📊 {report_path}")

def main():
    """Main function."""
    
    try:
        success = create_semantic_enhanced_pdf()
        
        if success:
            print(f"\n🎉 SEMANTIC ENHANCED PDF CREATED SUCCESSFULLY!")
            print(f"\n📁 Check the 'semantic_demo_output' folder for:")
            print(f"   📄 SEMANTIC_ENHANCED_Akshay_PA.pdf")
            print(f"   📊 SEMANTIC_ENHANCEMENT_REPORT.md")
            print(f"   💾 enhanced_data_comparison.json")
            
            print(f"\n🚀 This demonstrates semantic mapping with your existing data!")
            
        else:
            print(f"\n❌ Failed to create semantic enhanced PDF")
        
        return success
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
