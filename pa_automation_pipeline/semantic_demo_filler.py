#!/usr/bin/env python3
"""
Semantic <PERSON> - Demonstrates semantic mapping with your existing successful data.
Uses your working extracted data and applies semantic enhancements.
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any
import re

# Import your existing working components
from src.form_filler import fill_pa_form

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SemanticDemoMapper:
    """Simplified semantic mapper that works with your existing data."""
    
    def __init__(self):
        self.transformation_log = []
    
    def apply_semantic_enhancements(self, extracted_data: Dict[str, Any], pa_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Apply semantic enhancements to your existing extracted data."""
        
        logger.info("🧠 Applying semantic enhancements to extracted data...")
        
        enhanced_data = {}
        
        for field_id, value in extracted_data.items():
            if value is None:
                enhanced_data[field_id] = None
                continue
            
            # Apply semantic transformations based on field patterns
            enhanced_value, transformation = self._apply_semantic_transformation(field_id, value)
            enhanced_data[field_id] = enhanced_value
            
            if transformation != "none":
                self.transformation_log.append({
                    "field_id": field_id,
                    "original_value": value,
                    "enhanced_value": enhanced_value,
                    "transformation": transformation
                })
        
        return enhanced_data
    
    def _apply_semantic_transformation(self, field_id: str, value: Any) -> tuple[str, str]:
        """Apply semantic transformations based on field ID and value patterns."""
        
        if value is None:
            return None, "none"
        
        value_str = str(value).strip()
        
        # Phone number formatting
        if "phone" in field_id.lower() or field_id in ["Phone T"]:
            formatted_phone, transformation = self._format_phone(value_str)
            return formatted_phone, transformation
        
        # Date formatting
        if "date" in field_id.lower() or field_id in ["T13"]:  # T13 is DOB based on your data
            formatted_date, transformation = self._format_date(value_str)
            return formatted_date, transformation
        
        # Name formatting
        if "name" in field_id.lower() or field_id in ["T11", "T12"]:
            formatted_name, transformation = self._format_name(value_str)
            return formatted_name, transformation
        
        # Insurance info formatting
        if "insurance" in field_id.lower():
            formatted_insurance, transformation = self._format_insurance(value_str)
            return formatted_insurance, transformation
        
        # Medication dosing formatting
        if field_id in ["T17"]:  # Dosing information
            formatted_dosing, transformation = self._format_dosing(value_str)
            return formatted_dosing, transformation
        
        # General text cleaning
        return self._clean_text(value_str)
    
    def _format_phone(self, phone_str: str) -> tuple[str, str]:
        """Format phone numbers to standard format."""
        
        # Extract digits
        digits = re.sub(r'\D', '', phone_str)
        
        if len(digits) == 10:
            formatted = f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
            return formatted, "phone_formatted"
        elif len(digits) == 11 and digits[0] == '1':
            formatted = f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
            return formatted, "phone_formatted"
        
        return phone_str, "none"
    
    def _format_date(self, date_str: str) -> tuple[str, str]:
        """Format dates to MM/DD/YYYY."""
        
        # Try to match MM/DD/YYYY pattern
        if re.match(r'^\d{2}/\d{2}/\d{4}$', date_str):
            return date_str, "none"  # Already formatted
        
        # Try to match other patterns and convert
        patterns = [
            (r'(\d{4})-(\d{1,2})-(\d{1,2})', lambda m: f"{m.group(2).zfill(2)}/{m.group(3).zfill(2)}/{m.group(1)}"),
            (r'(\d{1,2})-(\d{1,2})-(\d{4})', lambda m: f"{m.group(1).zfill(2)}/{m.group(2).zfill(2)}/{m.group(3)}"),
        ]
        
        for pattern, formatter in patterns:
            match = re.match(pattern, date_str)
            if match:
                return formatter(match), "date_reformatted"
        
        return date_str, "none"
    
    def _format_name(self, name_str: str) -> tuple[str, str]:
        """Format names to proper case."""
        
        # Clean whitespace
        cleaned = ' '.join(name_str.strip().split())
        
        # Apply title case if all lowercase or all uppercase
        if cleaned.lower() == cleaned or cleaned.upper() == cleaned:
            formatted = cleaned.title()
            return formatted, "name_title_cased"
        
        return cleaned, "name_cleaned"
    
    def _format_insurance(self, insurance_str: str) -> tuple[str, str]:
        """Format insurance information."""
        
        # Clean up insurance info formatting
        lines = insurance_str.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # Clean up common patterns
                line = re.sub(r':\s+', ': ', line)  # Standardize colons
                line = re.sub(r'\s+', ' ', line)   # Remove extra spaces
                formatted_lines.append(line)
        
        formatted = '\n'.join(formatted_lines)
        
        if formatted != insurance_str:
            return formatted, "insurance_formatted"
        
        return insurance_str, "none"
    
    def _format_dosing(self, dosing_str: str) -> tuple[str, str]:
        """Format medication dosing information."""
        
        # Clean up dosing information
        lines = dosing_str.split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # Standardize common dosing abbreviations
                line = re.sub(r'\bIV\b', 'IV', line)
                line = re.sub(r'\bSQ\b', 'SQ', line)
                line = re.sub(r'\bmg\b', 'mg', line)
                formatted_lines.append(line)
        
        formatted = '\n'.join(formatted_lines)
        
        if formatted != dosing_str:
            return formatted, "dosing_formatted"
        
        return dosing_str, "none"
    
    def _clean_text(self, text_str: str) -> tuple[str, str]:
        """General text cleaning."""
        
        # Remove extra whitespace
        cleaned = ' '.join(text_str.strip().split())
        
        if cleaned != text_str:
            return cleaned, "text_cleaned"
        
        return text_str, "none"

def demonstrate_semantic_enhancement():
    """Demonstrate semantic enhancement with your existing successful data."""
    
    print("=" * 80)
    print("🧠 SEMANTIC ENHANCEMENT DEMONSTRATION")
    print("Using your existing successful Akshay data")
    print("=" * 80)
    
    # Load your existing successful data
    extracted_data_path = Path("fixed_output/Akshay_fixed_extracted.json")
    pa_schema_path = Path("fixed_output/Akshay_actual_fields.json")
    
    if not extracted_data_path.exists() or not pa_schema_path.exists():
        print("❌ Required data files not found!")
        return False
    
    # Load the data
    with open(extracted_data_path, 'r') as f:
        extraction_results = json.load(f)
    
    with open(pa_schema_path, 'r') as f:
        pa_schema_data = json.load(f)
    
    extracted_data = extraction_results["extracted_data"]
    pa_schema = pa_schema_data["fields"]
    
    print(f"📊 ORIGINAL EXTRACTED DATA:")
    print(f"   Total fields: {len(extracted_data)}")
    print(f"   Extraction rate: {extraction_results.get('extraction_rate', 'Unknown')}")
    
    # Show some original data
    print(f"\n📋 Sample Original Values:")
    sample_fields = ["T11", "T12", "T13", "Phone T", "T15", "T16"]
    for field in sample_fields:
        if field in extracted_data and extracted_data[field]:
            print(f"   {field}: '{extracted_data[field]}'")
    
    # Apply semantic enhancements
    print(f"\n🧠 APPLYING SEMANTIC ENHANCEMENTS...")
    mapper = SemanticDemoMapper()
    enhanced_data = mapper.apply_semantic_enhancements(extracted_data, pa_schema)
    
    # Show transformations
    if mapper.transformation_log:
        print(f"\n🔄 TRANSFORMATIONS APPLIED:")
        for i, log_entry in enumerate(mapper.transformation_log, 1):
            print(f"   {i}. {log_entry['field_id']} ({log_entry['transformation']}):")
            print(f"      Original: '{log_entry['original_value']}'")
            print(f"      Enhanced: '{log_entry['enhanced_value']}'")
            print()
    else:
        print(f"   No transformations needed - data already well formatted!")
    
    # Create enhanced schema for form filling
    enhanced_schema = {}
    for field_id, field_info in pa_schema.items():
        enhanced_schema[field_id] = {
            "acro_id": field_id,
            "semantic_meaning": field_info.get("field_name", field_id),
            "type": field_info.get("field_type", "text"),
            "human_name": field_info.get("field_name", field_id)
        }
    
    # Fill the PDF with enhanced data
    print(f"\n📝 FILLING PDF WITH SEMANTIC ENHANCED DATA...")
    
    pa_form_path = Path("Input Data/Akshay/pa.pdf")
    output_path = Path("semantic_demo_output")
    output_path.mkdir(exist_ok=True)
    
    filled_pdf_path = output_path / "SEMANTIC_ENHANCED_Akshay_PA.pdf"
    
    success = fill_pa_form(
        blank_pdf_path=pa_form_path,
        schema=enhanced_schema,
        extracted_data=enhanced_data,
        output_path=filled_pdf_path
    )
    
    if success:
        print(f"✅ SUCCESS! Semantic enhanced PDF created:")
        print(f"   📄 {filled_pdf_path}")
        
        # Generate comparison report
        report_path = output_path / "SEMANTIC_ENHANCEMENT_REPORT.md"
        generate_comparison_report(
            original_data=extracted_data,
            enhanced_data=enhanced_data,
            transformations=mapper.transformation_log,
            report_path=report_path
        )
        
        print(f"   📊 {report_path}")
        
        # Save enhanced results
        results_path = output_path / "SEMANTIC_ENHANCED_RESULTS.json"
        with open(results_path, 'w') as f:
            json.dump({
                "original_data": extracted_data,
                "enhanced_data": enhanced_data,
                "transformations": mapper.transformation_log,
                "enhancement_summary": {
                    "total_fields": len(enhanced_data),
                    "transformations_applied": len(mapper.transformation_log),
                    "transformation_rate": f"{len(mapper.transformation_log)/len(enhanced_data)*100:.1f}%"
                }
            }, f, indent=2)
        
        print(f"   💾 {results_path}")
        
        return True
    else:
        print(f"❌ PDF filling failed!")
        return False

def generate_comparison_report(original_data: Dict, enhanced_data: Dict, transformations: list, report_path: Path):
    """Generate a comparison report showing semantic enhancements."""
    
    with open(report_path, 'w') as f:
        f.write("# Semantic Enhancement Demonstration Report\n\n")
        f.write("## Overview\n\n")
        f.write("This report shows how semantic enhancements improve your existing PA automation data.\n\n")
        
        f.write("## Enhancement Summary\n\n")
        f.write(f"- **Total Fields**: {len(enhanced_data)}\n")
        f.write(f"- **Transformations Applied**: {len(transformations)}\n")
        f.write(f"- **Enhancement Rate**: {len(transformations)/len(enhanced_data)*100:.1f}%\n\n")
        
        if transformations:
            f.write("## Transformations Applied\n\n")
            for i, transform in enumerate(transformations, 1):
                f.write(f"### {i}. {transform['field_id']} - {transform['transformation']}\n\n")
                f.write(f"**Original Value:**\n```\n{transform['original_value']}\n```\n\n")
                f.write(f"**Enhanced Value:**\n```\n{transform['enhanced_value']}\n```\n\n")
                f.write("---\n\n")
        else:
            f.write("## No Transformations Needed\n\n")
            f.write("Your existing data is already well-formatted! This demonstrates the quality of your current extraction pipeline.\n\n")
        
        f.write("## Benefits of Semantic Enhancement\n\n")
        f.write("- ✅ **Consistent Formatting**: Standardized phone numbers, dates, names\n")
        f.write("- ✅ **Better Compatibility**: Values formatted for PDF form requirements\n")
        f.write("- ✅ **Quality Assurance**: Automatic validation and cleaning\n")
        f.write("- ✅ **Audit Trail**: Complete log of all transformations\n")
        f.write("- ✅ **Scalability**: Works with any PA form automatically\n\n")

def main():
    """Main demonstration function."""
    
    success = demonstrate_semantic_enhancement()
    
    if success:
        print(f"\n🎉 SEMANTIC ENHANCEMENT DEMONSTRATION COMPLETE!")
        print(f"\n📁 Check the 'semantic_demo_output' folder for:")
        print(f"   📄 SEMANTIC_ENHANCED_Akshay_PA.pdf - Filled PDF")
        print(f"   📊 SEMANTIC_ENHANCEMENT_REPORT.md - Detailed report")
        print(f"   💾 SEMANTIC_ENHANCED_RESULTS.json - Complete results")
        
        print(f"\n🚀 This demonstrates how semantic mapping enhances your existing")
        print(f"   successful pipeline with better formatting and standardization!")
    else:
        print(f"\n❌ Demonstration failed. Check the logs for details.")
    
    return success

if __name__ == "__main__":
    main()
