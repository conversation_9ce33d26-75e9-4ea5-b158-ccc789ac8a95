"""
Semantic Enhanced PA Automation Pipeline
Integrates AI-powered semantic mapping and value standardization.
"""

import json
import logging
import argparse
from pathlib import Path
from typing import Dict, Any

from src.intelligent_schema_generator import IntelligentSchemaGenerator
from src.intelligent_data_extractor import IntelligentDataExtractor
from src.semantic_mapper import SemanticMapper
from src.value_mapper import ValueMapper
from src.form_filler import fill_pa_form

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SemanticEnhancedPipeline:
    """Complete PA automation pipeline with semantic mapping and value standardization."""
    
    def __init__(self):
        """Initialize all pipeline components."""
        try:
            self.schema_generator = IntelligentSchemaGenerator()
            self.data_extractor = IntelligentDataExtractor()
            self.semantic_mapper = SemanticMapper()
            self.value_mapper = ValueMapper()
            logger.info("✅ All pipeline components initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize pipeline components: {e}")
            raise
    
    def process_patient_folder(self, 
                             patient_dir: Path, 
                             output_dir: Path,
                             schemas_dir: Path = None) -> Dict[str, Any]:
        """
        Process a single patient folder with semantic enhancement.
        
        Returns:
            Dict containing processing results and metadata
        """
        
        logger.info(f"🚀 Processing patient folder: {patient_dir.name}")
        
        # Validate input files
        pa_form_path = patient_dir / "PA.pdf"
        referral_path = patient_dir / "referral_package.pdf"
        
        # Try alternative naming conventions
        if not pa_form_path.exists():
            pa_form_path = patient_dir / "pa.pdf"
        
        if not pa_form_path.exists() or not referral_path.exists():
            logger.error(f"❌ Missing required files in {patient_dir}")
            logger.error(f"   PA form exists: {pa_form_path.exists()}")
            logger.error(f"   Referral exists: {referral_path.exists()}")
            return {"success": False, "error": "Missing required files"}
        
        try:
            # Stage 1: Generate Intelligent Schema
            logger.info("📋 Stage 1: Generating intelligent schema...")
            intelligent_schema = self.schema_generator.generate_intelligent_schema(pa_form_path)
            
            if not intelligent_schema:
                logger.error("❌ Schema generation failed")
                return {"success": False, "error": "Schema generation failed"}
            
            logger.info(f"✅ Generated schema with {len(intelligent_schema)} fields")
            
            # Stage 2: Extract Data with Chain-of-Thought
            logger.info("🔍 Stage 2: Extracting data with intelligent guidance...")
            extraction_results = self.data_extractor.extract_with_intelligent_schema(
                referral_path, intelligent_schema
            )
            
            if not extraction_results or not extraction_results.get("extracted_data"):
                logger.error("❌ Data extraction failed")
                return {"success": False, "error": "Data extraction failed"}
            
            extracted_data = extraction_results["extracted_data"]
            extraction_metadata = extraction_results.get("extraction_metadata", {})
            extraction_summary = extraction_results.get("extraction_summary", {})
            
            logger.info(f"✅ Extracted data: {extraction_summary}")
            
            # Stage 3: Semantic Mapping (NEW!)
            logger.info("🧠 Stage 3: Creating semantic mappings...")
            mapping_results = self.semantic_mapper.create_semantic_mapping(
                extracted_data, intelligent_schema
            )
            
            mapped_fields = mapping_results["mapped_fields"]
            mapping_metadata = mapping_results["mapping_metadata"]
            mapping_summary = mapping_results["mapping_summary"]
            
            logger.info(f"✅ Created semantic mappings: {mapping_summary}")
            
            # Stage 4: Value Standardization (NEW!)
            logger.info("🔧 Stage 4: Standardizing values...")
            standardization_results = self.value_mapper.standardize_values(
                mapped_fields, intelligent_schema
            )
            
            standardized_data = standardization_results["standardized_data"]
            standardization_log = standardization_results["standardization_log"]
            standardization_summary = standardization_results["standardization_summary"]
            
            logger.info(f"✅ Standardized values: {standardization_summary}")
            
            # Stage 5: Fill PA Form
            logger.info("📝 Stage 5: Filling PA form...")
            filled_pdf_path = output_dir / f"SEMANTIC_FILLED_{patient_dir.name}_{pa_form_path.name}"
            
            fill_success = fill_pa_form(
                pa_form_path, 
                intelligent_schema, 
                standardized_data, 
                filled_pdf_path
            )
            
            if not fill_success:
                logger.error("❌ Form filling failed")
                return {"success": False, "error": "Form filling failed"}
            
            logger.info(f"✅ Form filled successfully: {filled_pdf_path}")
            
            # Stage 6: Generate Comprehensive Report
            logger.info("📊 Stage 6: Generating comprehensive report...")
            report_data = {
                "patient_name": patient_dir.name,
                "processing_timestamp": extraction_results.get("extraction_summary", {}).get("timestamp", ""),
                "schema_info": {
                    "total_fields": len(intelligent_schema),
                    "field_categories": self._analyze_schema_categories(intelligent_schema)
                },
                "extraction_results": {
                    "summary": extraction_summary,
                    "metadata": extraction_metadata
                },
                "semantic_mapping": {
                    "summary": mapping_summary,
                    "transformation_log": mapping_results.get("transformation_log", [])
                },
                "value_standardization": {
                    "summary": standardization_summary,
                    "standardization_log": standardization_log
                },
                "form_filling": {
                    "success": fill_success,
                    "output_path": str(filled_pdf_path)
                }
            }
            
            # Save detailed results
            self._save_processing_results(patient_dir.name, report_data, output_dir)
            
            logger.info(f"🎉 Successfully processed {patient_dir.name}")
            
            return {
                "success": True,
                "patient_name": patient_dir.name,
                "filled_pdf_path": filled_pdf_path,
                "report_data": report_data
            }
            
        except Exception as e:
            logger.error(f"❌ Processing failed for {patient_dir.name}: {e}")
            return {"success": False, "error": str(e)}
    
    def _analyze_schema_categories(self, schema: Dict[str, Any]) -> Dict[str, int]:
        """Analyze schema field categories."""
        
        categories = {}
        for field_info in schema.values():
            category = field_info.get('category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
        
        return categories
    
    def _save_processing_results(self, patient_name: str, report_data: Dict[str, Any], output_dir: Path):
        """Save comprehensive processing results."""
        
        # Save JSON results
        json_path = output_dir / f"{patient_name}_SEMANTIC_RESULTS.json"
        with open(json_path, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        # Save markdown report
        md_path = output_dir / f"{patient_name}_SEMANTIC_REPORT.md"
        self._generate_markdown_report(report_data, md_path)
        
        logger.info(f"📄 Results saved: {json_path} and {md_path}")
    
    def _generate_markdown_report(self, report_data: Dict[str, Any], report_path: Path):
        """Generate comprehensive markdown report."""
        
        with open(report_path, 'w') as f:
            f.write(f"# Semantic Enhanced PA Processing Report\n\n")
            f.write(f"**Patient**: {report_data['patient_name']}\n")
            f.write(f"**Processed**: {report_data.get('processing_timestamp', 'Unknown')}\n\n")
            
            # Schema Analysis
            f.write("## 📋 Schema Analysis\n\n")
            schema_info = report_data["schema_info"]
            f.write(f"- **Total Fields**: {schema_info['total_fields']}\n")
            f.write("- **Field Categories**:\n")
            for category, count in schema_info["field_categories"].items():
                f.write(f"  - {category}: {count} fields\n")
            f.write("\n")
            
            # Extraction Results
            f.write("## 🔍 Data Extraction Results\n\n")
            extraction = report_data["extraction_results"]["summary"]
            f.write(f"- **Total Fields**: {extraction.get('total_fields', 0)}\n")
            f.write(f"- **Successfully Extracted**: {extraction.get('extracted_fields', 0)}\n")
            f.write(f"- **Extraction Rate**: {extraction.get('extraction_rate', '0%')}\n\n")
            
            # Semantic Mapping Results
            f.write("## 🧠 Semantic Mapping Results\n\n")
            mapping = report_data["semantic_mapping"]["summary"]
            f.write(f"- **Total Mappings**: {mapping.get('total_mappings', 0)}\n")
            f.write(f"- **High Confidence**: {mapping.get('high_confidence', 0)}\n")
            f.write(f"- **Medium Confidence**: {mapping.get('medium_confidence', 0)}\n")
            f.write(f"- **Low Confidence**: {mapping.get('low_confidence', 0)}\n\n")
            
            # Value Standardization Results
            f.write("## 🔧 Value Standardization Results\n\n")
            standardization = report_data["value_standardization"]["summary"]
            f.write(f"- **Total Fields**: {standardization.get('total_fields', 0)}\n")
            f.write(f"- **Transformed Fields**: {standardization.get('transformed_fields', 0)}\n")
            f.write(f"- **Transformation Rate**: {standardization.get('transformation_rate', '0%')}\n\n")
            
            # Form Filling Results
            f.write("## 📝 Form Filling Results\n\n")
            form_filling = report_data["form_filling"]
            status = "✅ Success" if form_filling["success"] else "❌ Failed"
            f.write(f"- **Status**: {status}\n")
            f.write(f"- **Output**: {form_filling.get('output_path', 'N/A')}\n\n")
            
            # Transformation Log
            if report_data["value_standardization"]["standardization_log"]:
                f.write("## 🔄 Value Transformations Applied\n\n")
                for log_entry in report_data["value_standardization"]["standardization_log"]:
                    f.write(f"### {log_entry['field_id']}\n")
                    f.write(f"- **Original**: `{log_entry['original_value']}`\n")
                    f.write(f"- **Standardized**: `{log_entry['standardized_value']}`\n")
                    f.write(f"- **Transformation**: {log_entry['transformation']}\n\n")


def main():
    """Main entry point for semantic enhanced pipeline."""
    
    parser = argparse.ArgumentParser(description="Semantic Enhanced PA Automation Pipeline")
    parser.add_argument("--input_dir", type=Path, default=Path("Input Data"), 
                       help="Directory containing patient folders")
    parser.add_argument("--output_dir", type=Path, default=Path("semantic_output"), 
                       help="Directory to save results")
    parser.add_argument("--patient", type=str, help="Process specific patient folder only")
    
    args = parser.parse_args()
    
    # Create output directory
    args.output_dir.mkdir(exist_ok=True)
    
    # Initialize pipeline
    try:
        pipeline = SemanticEnhancedPipeline()
    except Exception as e:
        logger.error(f"❌ Failed to initialize pipeline: {e}")
        return
    
    # Process patients
    if args.patient:
        # Process specific patient
        patient_dir = args.input_dir / args.patient
        if patient_dir.exists():
            result = pipeline.process_patient_folder(patient_dir, args.output_dir)
            if result["success"]:
                logger.info(f"🎉 Successfully processed {args.patient}")
            else:
                logger.error(f"❌ Failed to process {args.patient}: {result.get('error')}")
        else:
            logger.error(f"❌ Patient folder not found: {patient_dir}")
    else:
        # Process all patients
        success_count = 0
        total_count = 0
        
        for patient_dir in sorted(args.input_dir.iterdir()):
            if patient_dir.is_dir():
                total_count += 1
                result = pipeline.process_patient_folder(patient_dir, args.output_dir)
                if result["success"]:
                    success_count += 1
        
        logger.info(f"🏁 Processing complete: {success_count}/{total_count} patients processed successfully")


if __name__ == "__main__":
    main()
