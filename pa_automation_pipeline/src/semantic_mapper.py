"""
AI-Powered Semantic Mapper - Inspired by Osmos AI mapping approach.
Maps extracted data fields to PA form fields using semantic understanding.
"""

import json
import logging
import os
import time
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime

import openai
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SemanticMapper:
    """AI-powered semantic mapping between extracted data and PA form fields."""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize OpenAI for advanced reasoning
        openai_key = os.getenv("OPENAI_API_KEY")
        if not openai_key:
            raise ValueError("OPENAI_API_KEY not found in .env file.")
        
        openai.api_key = openai_key
        self.client = openai.OpenAI(api_key=openai_key)
        
        # Load mapping history for learning
        self.mapping_history = self._load_mapping_history()
        self.confidence_threshold = 0.7
    
    def _load_mapping_history(self) -> Dict[str, Any]:
        """Load historical successful mappings for better predictions."""
        history_path = Path("mapping_history.json")
        if history_path.exists():
            with open(history_path, 'r') as f:
                return json.load(f)
        return {
            "successful_mappings": {},
            "field_patterns": {},
            "value_transformations": {},
            "confidence_scores": {}
        }
    
    def _save_mapping_history(self):
        """Save mapping history for future use."""
        with open("mapping_history.json", 'w') as f:
            json.dump(self.mapping_history, f, indent=2)
    
    def create_semantic_mapping(self, 
                              extracted_data: Dict[str, Any], 
                              pa_schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create semantic mappings between extracted data and PA form fields.
        Uses OpenAI O1 for advanced reasoning about field relationships.
        """
        
        logger.info("Creating semantic mappings using AI reasoning...")
        
        # Prepare data for mapping analysis
        extracted_fields = list(extracted_data.keys())
        pa_fields = []
        
        for field_id, field_info in pa_schema.items():
            semantic_meaning = field_info.get('semantic_meaning', field_id)
            category = field_info.get('category', 'unknown')
            field_type = field_info.get('field_type', 'text')
            
            pa_fields.append({
                "field_id": field_id,
                "semantic_meaning": semantic_meaning,
                "category": category,
                "field_type": field_type,
                "extraction_guidance": field_info.get('extraction_guidance', '')
            })
        
        # Create mapping prompt for O1 reasoning
        mapping_prompt = self._create_mapping_prompt(extracted_fields, pa_fields)
        
        try:
            # Use O1 model for advanced reasoning
            response = self.client.chat.completions.create(
                model="o1-preview",
                messages=[
                    {
                        "role": "user", 
                        "content": mapping_prompt
                    }
                ],
                temperature=0.1
            )
            
            # Parse the mapping response
            mapping_result = self._parse_mapping_response(response.choices[0].message.content)
            
            # Apply the mappings to create final mapped data
            mapped_data = self._apply_semantic_mappings(extracted_data, mapping_result, pa_schema)
            
            # Update mapping history for learning
            self._update_mapping_history(mapping_result)
            
            logger.info(f"Created {len(mapped_data['mapped_fields'])} semantic mappings")
            
            return mapped_data
            
        except Exception as e:
            logger.error(f"Semantic mapping failed: {e}")
            # Fallback to direct mapping
            return self._create_fallback_mapping(extracted_data, pa_schema)
    
    def _create_mapping_prompt(self, extracted_fields: List[str], pa_fields: List[Dict]) -> str:
        """Create detailed mapping prompt for O1 reasoning."""
        
        prompt = """# AI-Powered Semantic Field Mapping for PA Forms

You are an expert at mapping healthcare data fields between different systems. Your task is to create semantic mappings between extracted referral data and PA form fields.

## CONTEXT
You're working with Prior Authorization (PA) forms in healthcare. The extracted data comes from referral packages (clinical notes, insurance cards, etc.) and needs to be mapped to specific PA form fields.

## EXTRACTED DATA FIELDS
These are the fields extracted from the referral package:
"""
        
        for field in extracted_fields:
            prompt += f"- {field}\n"
        
        prompt += "\n## PA FORM FIELDS\nThese are the target fields in the PA form:\n"
        
        for field in pa_fields:
            prompt += f"""
### {field['field_id']} ({field['category']})
- **Semantic Meaning**: {field['semantic_meaning']}
- **Field Type**: {field['field_type']}
- **Guidance**: {field['extraction_guidance']}
"""
        
        prompt += """

## MAPPING RULES

### 1. DIRECT MAPPINGS
Map extracted fields directly to PA fields when they have the same semantic meaning:
- "patient_first_name" → field asking for patient's first name
- "prescriber_npi" → field asking for prescriber's NPI number

### 2. COMPLEX MAPPINGS
Handle cases where one extracted field maps to multiple PA fields:
- "full_name" → separate "first_name" and "last_name" fields
- "address" → separate "street", "city", "state", "zip" fields
- "phone_number" → "phone" and potentially "fax" if same number

### 3. VALUE TRANSFORMATIONS
Apply necessary transformations:
- Date formats: "2024-01-15" → "01/15/2024"
- Phone formats: "**********" → "(*************"
- Boolean values: "Yes"/"True" → "/Yes" for checkboxes

### 4. CONFIDENCE SCORING
Rate each mapping's confidence:
- **High (0.9-1.0)**: Exact semantic match, clear relationship
- **Medium (0.6-0.8)**: Reasonable match, some interpretation needed
- **Low (0.3-0.5)**: Uncertain match, requires validation

## OUTPUT FORMAT

Return a JSON object with this structure:

```json
{
  "mappings": [
    {
      "extracted_field": "patient_first_name",
      "pa_field_id": "T11",
      "mapping_type": "direct",
      "confidence": 0.95,
      "transformation": "none",
      "reasoning": "Direct semantic match for patient's first name"
    },
    {
      "extracted_field": "full_name",
      "pa_field_id": ["T11", "T12"],
      "mapping_type": "split",
      "confidence": 0.85,
      "transformation": "split_name",
      "reasoning": "Split full name into first and last name components"
    }
  ],
  "unmapped_extracted": ["field1", "field2"],
  "unmapped_pa_fields": ["T99"],
  "mapping_summary": {
    "total_mappings": 15,
    "high_confidence": 12,
    "medium_confidence": 2,
    "low_confidence": 1
  }
}
```

## HEALTHCARE-SPECIFIC CONSIDERATIONS

- **Patient Demographics**: Names, DOB, addresses, phone numbers
- **Insurance Information**: Member IDs, group numbers, payer information
- **Clinical Data**: Diagnosis codes (ICD-10), medications, dosages
- **Provider Information**: NPI numbers, practice details, credentials
- **Authorization Details**: Request dates, urgency, clinical justification

Think through each mapping systematically. Consider the healthcare context and PA form requirements. Prioritize accuracy over completeness.

Begin your analysis now."""
        
        return prompt
    
    def _parse_mapping_response(self, response_text: str) -> Dict[str, Any]:
        """Parse the O1 mapping response."""
        
        try:
            # Look for JSON in the response
            import re
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_text, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            else:
                # Try to find JSON object directly
                start = response_text.find('{')
                end = response_text.rfind('}') + 1
                if start != -1 and end > start:
                    return json.loads(response_text[start:end])
                else:
                    logger.error("Could not parse JSON from mapping response")
                    return {"mappings": [], "unmapped_extracted": [], "unmapped_pa_fields": []}
                    
        except json.JSONDecodeError as e:
            logger.error(f"JSON parsing failed for mapping response: {e}")
            return {"mappings": [], "unmapped_extracted": [], "unmapped_pa_fields": []}
    
    def _apply_semantic_mappings(self, 
                               extracted_data: Dict[str, Any], 
                               mapping_result: Dict[str, Any],
                               pa_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Apply the semantic mappings to create final mapped data."""
        
        mapped_fields = {}
        mapping_metadata = {}
        transformation_log = []
        
        for mapping in mapping_result.get("mappings", []):
            extracted_field = mapping["extracted_field"]
            pa_field_ids = mapping["pa_field_id"]
            mapping_type = mapping["mapping_type"]
            confidence = mapping["confidence"]
            transformation = mapping.get("transformation", "none")
            
            # Skip low confidence mappings
            if confidence < self.confidence_threshold:
                logger.warning(f"Skipping low confidence mapping: {extracted_field} -> {pa_field_ids} (confidence: {confidence})")
                continue
            
            # Get the extracted value
            extracted_value = extracted_data.get(extracted_field)
            if extracted_value is None:
                continue
            
            # Handle different mapping types
            if mapping_type == "direct":
                # Direct 1:1 mapping
                pa_field_id = pa_field_ids if isinstance(pa_field_ids, str) else pa_field_ids[0]
                transformed_value = self._transform_value(extracted_value, transformation, pa_schema.get(pa_field_id, {}))
                
                mapped_fields[pa_field_id] = transformed_value
                mapping_metadata[pa_field_id] = {
                    "source_field": extracted_field,
                    "confidence": confidence,
                    "transformation": transformation,
                    "reasoning": mapping["reasoning"]
                }
                
            elif mapping_type == "split":
                # Split one field into multiple
                if transformation == "split_name" and isinstance(pa_field_ids, list) and len(pa_field_ids) >= 2:
                    name_parts = str(extracted_value).strip().split()
                    if len(name_parts) >= 2:
                        # First name
                        mapped_fields[pa_field_ids[0]] = name_parts[0]
                        mapping_metadata[pa_field_ids[0]] = {
                            "source_field": extracted_field,
                            "confidence": confidence,
                            "transformation": "first_name_from_full",
                            "reasoning": f"First name extracted from full name: {extracted_value}"
                        }
                        
                        # Last name (everything after first name)
                        mapped_fields[pa_field_ids[1]] = " ".join(name_parts[1:])
                        mapping_metadata[pa_field_ids[1]] = {
                            "source_field": extracted_field,
                            "confidence": confidence,
                            "transformation": "last_name_from_full",
                            "reasoning": f"Last name extracted from full name: {extracted_value}"
                        }
            
            # Log the transformation
            transformation_log.append({
                "extracted_field": extracted_field,
                "extracted_value": extracted_value,
                "pa_field_ids": pa_field_ids,
                "mapping_type": mapping_type,
                "transformation": transformation,
                "confidence": confidence
            })
        
        return {
            "mapped_fields": mapped_fields,
            "mapping_metadata": mapping_metadata,
            "transformation_log": transformation_log,
            "mapping_summary": {
                "total_mappings": len(mapped_fields),
                "high_confidence": sum(1 for m in mapping_metadata.values() if m["confidence"] >= 0.8),
                "medium_confidence": sum(1 for m in mapping_metadata.values() if 0.6 <= m["confidence"] < 0.8),
                "low_confidence": sum(1 for m in mapping_metadata.values() if m["confidence"] < 0.6)
            }
        }

    def _transform_value(self, value: Any, transformation: str, field_info: Dict[str, Any]) -> str:
        """Apply value transformations based on field type and requirements."""

        if value is None:
            return None

        value_str = str(value).strip()

        if transformation == "none":
            return value_str

        # Date transformations
        if transformation == "format_date" or "date" in field_info.get("semantic_meaning", "").lower():
            return self._format_date(value_str)

        # Phone number transformations
        if transformation == "format_phone" or "phone" in field_info.get("semantic_meaning", "").lower():
            return self._format_phone(value_str)

        # Checkbox transformations
        if field_info.get("field_type") == "checkbox" or field_info.get("field_type") == "button":
            return self._format_checkbox(value_str)

        # NPI transformations
        if "npi" in field_info.get("semantic_meaning", "").lower():
            return self._format_npi(value_str)

        return value_str

    def _format_date(self, date_str: str) -> str:
        """Format date to MM/DD/YYYY."""
        import re

        # Try different date patterns
        patterns = [
            r"(\d{4})-(\d{1,2})-(\d{1,2})",  # YYYY-MM-DD
            r"(\d{1,2})/(\d{1,2})/(\d{4})",  # MM/DD/YYYY or DD/MM/YYYY
            r"(\d{1,2})-(\d{1,2})-(\d{4})",  # MM-DD-YYYY
        ]

        for pattern in patterns:
            match = re.search(pattern, date_str)
            if match:
                if pattern == patterns[0]:  # YYYY-MM-DD
                    year, month, day = match.groups()
                    return f"{month.zfill(2)}/{day.zfill(2)}/{year}"
                else:  # MM/DD/YYYY or MM-DD-YYYY
                    part1, part2, year = match.groups()
                    return f"{part1.zfill(2)}/{part2.zfill(2)}/{year}"

        return date_str  # Return original if no pattern matches

    def _format_phone(self, phone_str: str) -> str:
        """Format phone number to (XXX) XXX-XXXX."""
        import re

        # Extract digits only
        digits = re.sub(r'\D', '', phone_str)

        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        elif len(digits) == 11 and digits[0] == '1':
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}"

        return phone_str  # Return original if can't format

    def _format_checkbox(self, value_str: str) -> str:
        """Format value for checkbox fields."""

        true_values = ["yes", "y", "true", "1", "checked", "on"]
        if value_str.lower() in true_values:
            return "/Yes"

        return value_str

    def _format_npi(self, npi_str: str) -> str:
        """Format NPI number."""
        import re

        # Extract digits only
        digits = re.sub(r'\D', '', npi_str)

        if len(digits) == 10:
            return digits

        return npi_str  # Return original if not 10 digits

    def _create_fallback_mapping(self, extracted_data: Dict[str, Any], pa_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback direct mapping when AI mapping fails."""

        logger.warning("Using fallback direct mapping")

        mapped_fields = {}
        mapping_metadata = {}

        # Try direct field name matching
        for field_id, field_info in pa_schema.items():
            semantic_meaning = field_info.get('semantic_meaning', field_id).lower()

            # Look for exact matches in extracted data
            for extracted_field, value in extracted_data.items():
                if value is not None and extracted_field.lower() in semantic_meaning:
                    mapped_fields[field_id] = str(value)
                    mapping_metadata[field_id] = {
                        "source_field": extracted_field,
                        "confidence": 0.5,
                        "transformation": "fallback_direct",
                        "reasoning": "Fallback direct mapping based on field name similarity"
                    }
                    break

        return {
            "mapped_fields": mapped_fields,
            "mapping_metadata": mapping_metadata,
            "transformation_log": [],
            "mapping_summary": {
                "total_mappings": len(mapped_fields),
                "high_confidence": 0,
                "medium_confidence": 0,
                "low_confidence": len(mapped_fields)
            }
        }

    def _update_mapping_history(self, mapping_result: Dict[str, Any]):
        """Update mapping history for learning."""

        timestamp = datetime.now().isoformat()

        for mapping in mapping_result.get("mappings", []):
            extracted_field = mapping["extracted_field"]
            pa_field_id = mapping["pa_field_id"]
            confidence = mapping["confidence"]

            # Store successful high-confidence mappings
            if confidence >= 0.8:
                key = f"{extracted_field}->{pa_field_id}"
                if key not in self.mapping_history["successful_mappings"]:
                    self.mapping_history["successful_mappings"][key] = []

                self.mapping_history["successful_mappings"][key].append({
                    "timestamp": timestamp,
                    "confidence": confidence,
                    "reasoning": mapping["reasoning"]
                })

        # Save updated history
        self._save_mapping_history()

    def learn_from_correction(self, original_mapping: Dict[str, Any], corrected_mapping: Dict[str, Any]):
        """Learn from human corrections to improve future mappings."""

        logger.info("Learning from human corrections...")

        timestamp = datetime.now().isoformat()

        # Store corrections for future reference
        if "corrections" not in self.mapping_history:
            self.mapping_history["corrections"] = []

        self.mapping_history["corrections"].append({
            "timestamp": timestamp,
            "original": original_mapping,
            "corrected": corrected_mapping
        })

        # Update confidence scores based on corrections
        for field_id, corrected_value in corrected_mapping.items():
            original_value = original_mapping.get(field_id)
            if original_value != corrected_value:
                # Lower confidence for this type of mapping
                if field_id in self.mapping_history["confidence_scores"]:
                    self.mapping_history["confidence_scores"][field_id] *= 0.9
                else:
                    self.mapping_history["confidence_scores"][field_id] = 0.5

        self._save_mapping_history()
        logger.info("Mapping corrections saved for future learning")
