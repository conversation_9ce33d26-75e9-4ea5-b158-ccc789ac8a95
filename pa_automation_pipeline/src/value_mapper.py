"""
AI-Powered Value Mapper - Standardizes and normalizes field values.
Inspired by Osmos value mapping capabilities.
"""

import json
import logging
import os
import re
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

import openai
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ValueMapper:
    """AI-powered value standardization and normalization."""
    
    def __init__(self):
        load_dotenv()
        
        # Initialize OpenAI
        openai_key = os.getenv("OPENAI_API_KEY")
        if not openai_key:
            raise ValueError("OPENAI_API_KEY not found in .env file.")
        
        self.client = openai.OpenAI(api_key=openai_key)
        
        # Load value mapping patterns
        self.value_patterns = self._load_value_patterns()
    
    def _load_value_patterns(self) -> Dict[str, Any]:
        """Load common value mapping patterns."""
        
        return {
            "boolean_mappings": {
                "true_values": ["yes", "y", "true", "1", "checked", "on", "positive", "confirmed"],
                "false_values": ["no", "n", "false", "0", "unchecked", "off", "negative", "denied"],
                "checkbox_format": "/Yes"
            },
            "date_patterns": [
                r"(\d{4})-(\d{1,2})-(\d{1,2})",  # YYYY-MM-DD
                r"(\d{1,2})/(\d{1,2})/(\d{4})",  # MM/DD/YYYY
                r"(\d{1,2})-(\d{1,2})-(\d{4})",  # MM-DD-YYYY
                r"(\d{1,2})\.(\d{1,2})\.(\d{4})", # MM.DD.YYYY
            ],
            "phone_patterns": [
                r"(\d{3})[\s\-\.]?(\d{3})[\s\-\.]?(\d{4})",  # Various phone formats
                r"\(?(\d{3})\)?[\s\-\.]?(\d{3})[\s\-\.]?(\d{4})",  # With optional parentheses
            ],
            "medical_codes": {
                "icd10_pattern": r"[A-TV-Z]\d{2,3}(\.\d{1,4})?",
                "npi_pattern": r"\d{10}",
                "ndc_pattern": r"\d{4,5}-\d{3,4}-\d{1,2}"
            }
        }
    
    def standardize_values(self, 
                          mapped_data: Dict[str, Any], 
                          pa_schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standardize values based on field types and PA form requirements.
        """
        
        logger.info("Standardizing values for PA form compatibility...")
        
        standardized_data = {}
        standardization_log = []
        
        for field_id, value in mapped_data.items():
            if value is None:
                standardized_data[field_id] = None
                continue
            
            field_info = pa_schema.get(field_id, {})
            field_type = field_info.get('field_type', 'text')
            semantic_meaning = field_info.get('semantic_meaning', '').lower()
            
            # Apply field-specific standardization
            standardized_value, transformation_applied = self._standardize_single_value(
                value, field_type, semantic_meaning, field_id
            )
            
            standardized_data[field_id] = standardized_value
            
            if transformation_applied != "none":
                standardization_log.append({
                    "field_id": field_id,
                    "original_value": value,
                    "standardized_value": standardized_value,
                    "transformation": transformation_applied,
                    "field_type": field_type
                })
        
        return {
            "standardized_data": standardized_data,
            "standardization_log": standardization_log,
            "standardization_summary": {
                "total_fields": len(mapped_data),
                "transformed_fields": len(standardization_log),
                "transformation_rate": f"{len(standardization_log)/len(mapped_data)*100:.1f}%"
            }
        }
    
    def _standardize_single_value(self, 
                                 value: Any, 
                                 field_type: str, 
                                 semantic_meaning: str, 
                                 field_id: str) -> tuple[str, str]:
        """Standardize a single value based on its context."""
        
        if value is None:
            return None, "none"
        
        value_str = str(value).strip()
        
        # Handle different field types
        if field_type in ["checkbox", "button"]:
            return self._standardize_boolean(value_str)
        
        elif "date" in semantic_meaning or "dob" in semantic_meaning:
            return self._standardize_date(value_str)
        
        elif "phone" in semantic_meaning or "fax" in semantic_meaning:
            return self._standardize_phone(value_str)
        
        elif "npi" in semantic_meaning:
            return self._standardize_npi(value_str)
        
        elif "diagnosis" in semantic_meaning or "icd" in semantic_meaning:
            return self._standardize_icd10(value_str)
        
        elif "name" in semantic_meaning:
            return self._standardize_name(value_str)
        
        elif "address" in semantic_meaning:
            return self._standardize_address(value_str)
        
        else:
            # General text cleaning
            return self._standardize_text(value_str)
    
    def _standardize_boolean(self, value_str: str) -> tuple[str, str]:
        """Standardize boolean values for checkboxes."""
        
        value_lower = value_str.lower().strip()
        
        if value_lower in self.value_patterns["boolean_mappings"]["true_values"]:
            return "/Yes", "boolean_to_checkbox"
        elif value_lower in self.value_patterns["boolean_mappings"]["false_values"]:
            return "", "boolean_to_empty"
        
        return value_str, "none"
    
    def _standardize_date(self, date_str: str) -> tuple[str, str]:
        """Standardize date to MM/DD/YYYY format."""
        
        for pattern in self.value_patterns["date_patterns"]:
            match = re.search(pattern, date_str)
            if match:
                groups = match.groups()
                
                if pattern == self.value_patterns["date_patterns"][0]:  # YYYY-MM-DD
                    year, month, day = groups
                    return f"{month.zfill(2)}/{day.zfill(2)}/{year}", "date_reformatted"
                else:  # MM/DD/YYYY, MM-DD-YYYY, MM.DD.YYYY
                    part1, part2, year = groups
                    return f"{part1.zfill(2)}/{part2.zfill(2)}/{year}", "date_reformatted"
        
        return date_str, "none"
    
    def _standardize_phone(self, phone_str: str) -> tuple[str, str]:
        """Standardize phone number to (XXX) XXX-XXXX format."""
        
        # Extract digits only
        digits = re.sub(r'\D', '', phone_str)
        
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}", "phone_formatted"
        elif len(digits) == 11 and digits[0] == '1':
            return f"({digits[1:4]}) {digits[4:7]}-{digits[7:]}", "phone_formatted"
        
        return phone_str, "none"
    
    def _standardize_npi(self, npi_str: str) -> tuple[str, str]:
        """Standardize NPI number."""
        
        # Extract digits only
        digits = re.sub(r'\D', '', npi_str)
        
        if len(digits) == 10:
            return digits, "npi_cleaned"
        
        return npi_str, "none"
    
    def _standardize_icd10(self, icd_str: str) -> tuple[str, str]:
        """Standardize ICD-10 codes."""
        
        # Look for ICD-10 pattern
        match = re.search(self.value_patterns["medical_codes"]["icd10_pattern"], icd_str.upper())
        if match:
            return match.group(0), "icd10_extracted"
        
        return icd_str, "none"
    
    def _standardize_name(self, name_str: str) -> tuple[str, str]:
        """Standardize names (proper case, trim whitespace)."""
        
        # Basic name cleaning
        cleaned = ' '.join(name_str.strip().split())  # Remove extra whitespace
        
        # Apply title case for names
        if cleaned.lower() != cleaned and cleaned.upper() != cleaned:
            # Already has mixed case, keep as is
            return cleaned, "name_cleaned"
        else:
            # Apply title case
            title_cased = cleaned.title()
            return title_cased, "name_title_cased"
    
    def _standardize_address(self, address_str: str) -> tuple[str, str]:
        """Standardize addresses."""
        
        # Basic address cleaning
        cleaned = ' '.join(address_str.strip().split())
        
        # Common address abbreviations
        abbreviations = {
            " Street": " St",
            " Avenue": " Ave",
            " Boulevard": " Blvd",
            " Drive": " Dr",
            " Road": " Rd",
            " Lane": " Ln",
            " Court": " Ct"
        }
        
        standardized = cleaned
        transformation = "address_cleaned"
        
        for full, abbrev in abbreviations.items():
            if full in standardized:
                standardized = standardized.replace(full, abbrev)
                transformation = "address_abbreviated"
        
        return standardized, transformation
    
    def _standardize_text(self, text_str: str) -> tuple[str, str]:
        """General text standardization."""
        
        # Basic text cleaning
        cleaned = ' '.join(text_str.strip().split())
        
        if cleaned != text_str:
            return cleaned, "text_cleaned"
        
        return text_str, "none"
    
    def create_value_mapping_with_ai(self, 
                                   field_values: List[str], 
                                   target_categories: List[str],
                                   field_context: str) -> Dict[str, Any]:
        """
        Use AI to map field values to target categories.
        Similar to Osmos value mapping functionality.
        """
        
        logger.info(f"Creating AI value mapping for {field_context}...")
        
        mapping_prompt = f"""# AI Value Mapping for Healthcare PA Forms

You are mapping values from source data to standardized categories for a PA form field.

## FIELD CONTEXT
Field: {field_context}

## SOURCE VALUES
These are the actual values found in the data:
{json.dumps(field_values, indent=2)}

## TARGET CATEGORIES
These are the standardized categories they should map to:
{json.dumps(target_categories, indent=2)}

## MAPPING RULES
1. Map each source value to the most appropriate target category
2. Multiple source values can map to the same target category
3. Use semantic understanding (e.g., "Male" and "M" both map to "Male")
4. Consider medical/healthcare context
5. Provide confidence scores for each mapping

## OUTPUT FORMAT
Return JSON with this structure:

```json
{{
  "mappings": [
    {{
      "source_value": "M",
      "target_category": "Male",
      "confidence": 0.95,
      "reasoning": "Standard abbreviation for Male"
    }}
  ],
  "unmapped_values": ["value1"],
  "mapping_summary": {{
    "total_mappings": 5,
    "high_confidence": 4,
    "needs_review": 1
  }}
}}
```

Create the mappings now."""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": mapping_prompt}],
                temperature=0.1
            )
            
            # Parse response
            import re
            response_text = response.choices[0].message.content
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_text, re.DOTALL)
            
            if json_match:
                return json.loads(json_match.group(1))
            else:
                logger.error("Could not parse AI value mapping response")
                return {"mappings": [], "unmapped_values": field_values}
                
        except Exception as e:
            logger.error(f"AI value mapping failed: {e}")
            return {"mappings": [], "unmapped_values": field_values}
