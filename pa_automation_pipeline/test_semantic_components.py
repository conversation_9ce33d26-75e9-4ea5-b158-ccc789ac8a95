#!/usr/bin/env python3
"""
Simple test script for semantic mapping components.
Tests individual components without running the full pipeline.
"""

import sys
import json
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Test if all required modules can be imported."""
    
    print("🧪 Testing component imports...")
    
    try:
        import openai
        print("✅ OpenAI imported successfully")
    except ImportError as e:
        print(f"❌ OpenAI import failed: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"❌ Google Generative AI import failed: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv imported successfully")
    except ImportError as e:
        print(f"❌ python-dotenv import failed: {e}")
        return False
    
    try:
        from pypdf import PdfReader, PdfWriter
        print("✅ pypdf imported successfully")
    except ImportError as e:
        print(f"❌ pypdf import failed: {e}")
        return False
    
    return True

def test_api_keys():
    """Test if API keys are properly configured."""
    
    print("\n🔑 Testing API key configuration...")
    
    try:
        from dotenv import load_dotenv
        import os
        
        load_dotenv()
        
        gemini_key = os.getenv("GEMINI_API_KEY")
        openai_key = os.getenv("OPENAI_API_KEY")
        
        if gemini_key:
            print("✅ Gemini API key found")
        else:
            print("❌ Gemini API key not found")
            return False
        
        if openai_key:
            print("✅ OpenAI API key found")
        else:
            print("❌ OpenAI API key not found")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API key test failed: {e}")
        return False

def test_semantic_mapper():
    """Test the semantic mapper component."""
    
    print("\n🧠 Testing Semantic Mapper...")
    
    try:
        from src.semantic_mapper import SemanticMapper
        
        # Create test data
        extracted_data = {
            "patient_first_name": "John",
            "patient_last_name": "Doe", 
            "date_of_birth": "1990-01-15",
            "phone_number": "**********",
            "full_name": "John Doe"
        }
        
        pa_schema = {
            "T11": {
                "semantic_meaning": "patient_first_name",
                "category": "patient_demographics",
                "field_type": "text"
            },
            "T12": {
                "semantic_meaning": "patient_last_name", 
                "category": "patient_demographics",
                "field_type": "text"
            },
            "T13": {
                "semantic_meaning": "patient_date_of_birth",
                "category": "patient_demographics", 
                "field_type": "text"
            }
        }
        
        print("   📝 Created test data")
        print(f"   📊 Extracted fields: {list(extracted_data.keys())}")
        print(f"   📋 PA schema fields: {list(pa_schema.keys())}")
        
        # Test mapper initialization (without actually calling OpenAI)
        print("   🔧 Testing mapper initialization...")
        
        # This will test the initialization without making API calls
        mapper = SemanticMapper()
        print("✅ Semantic Mapper initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Semantic Mapper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_value_mapper():
    """Test the value mapper component."""
    
    print("\n🔧 Testing Value Mapper...")
    
    try:
        from src.value_mapper import ValueMapper
        
        # Create test data
        test_values = {
            "T11": "john",
            "T12": "doe",
            "T13": "1990-01-15",
            "T14": "**********",
            "T15": "yes"
        }
        
        pa_schema = {
            "T11": {"semantic_meaning": "patient_first_name", "field_type": "text"},
            "T12": {"semantic_meaning": "patient_last_name", "field_type": "text"},
            "T13": {"semantic_meaning": "patient_date_of_birth", "field_type": "text"},
            "T14": {"semantic_meaning": "patient_phone", "field_type": "text"},
            "T15": {"semantic_meaning": "consent_checkbox", "field_type": "checkbox"}
        }
        
        print("   📝 Created test data for value standardization")
        
        # Test mapper initialization
        mapper = ValueMapper()
        print("   🔧 Value Mapper initialized successfully")
        
        # Test standardization without API calls
        results = mapper.standardize_values(test_values, pa_schema)
        
        print("   ✅ Value standardization completed")
        print(f"   📊 Standardized {len(results['standardized_data'])} values")
        
        # Show some results
        standardized = results['standardized_data']
        log = results['standardization_log']
        
        print("   📋 Standardization results:")
        for field_id, value in standardized.items():
            original = test_values.get(field_id)
            if original != value:
                print(f"      {field_id}: '{original}' → '{value}'")
            else:
                print(f"      {field_id}: '{value}' (no change)")
        
        return True
        
    except Exception as e:
        print(f"❌ Value Mapper test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_input_data():
    """Test if input data is available."""
    
    print("\n📁 Testing input data availability...")
    
    input_dir = Path("Input Data")
    
    if not input_dir.exists():
        print(f"❌ Input directory not found: {input_dir}")
        return False
    
    print(f"✅ Input directory found: {input_dir}")
    
    # Check for patient folders
    patient_folders = []
    for item in input_dir.iterdir():
        if item.is_dir():
            pa_form = item / "PA.pdf"
            pa_form_alt = item / "pa.pdf"
            referral = item / "referral_package.pdf"
            
            if (pa_form.exists() or pa_form_alt.exists()) and referral.exists():
                patient_folders.append(item.name)
    
    if patient_folders:
        print(f"✅ Found {len(patient_folders)} valid patient folders:")
        for folder in patient_folders:
            print(f"   - {folder}")
        return True
    else:
        print("❌ No valid patient folders found")
        return False

def main():
    """Run all component tests."""
    
    print("=" * 80)
    print("🧪 SEMANTIC MAPPING COMPONENTS TEST")
    print("=" * 80)
    
    tests = [
        ("Import Test", test_imports),
        ("API Keys Test", test_api_keys),
        ("Input Data Test", test_input_data),
        ("Value Mapper Test", test_value_mapper),
        ("Semantic Mapper Test", test_semantic_mapper),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*80}")
    print(f"🏁 TEST SUMMARY: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All component tests passed! Ready for semantic enhanced pipeline.")
        
        print(f"\n🚀 NEXT STEPS:")
        print(f"1. Run the full semantic enhanced pipeline:")
        print(f"   python semantic_enhanced_pipeline.py --patient Akshay")
        print(f"2. Or run the comprehensive test:")
        print(f"   python test_semantic_pipeline.py")
        
    else:
        print("❌ Some tests failed. Please fix the issues before running the full pipeline.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
