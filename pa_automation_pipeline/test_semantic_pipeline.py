#!/usr/bin/env python3
"""
Test script for the Semantic Enhanced PA Automation Pipeline.
Tests the new AI-powered semantic mapping and value standardization features.
"""

import sys
import json
import logging
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from semantic_enhanced_pipeline import SemanticEnhancedPipeline

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_semantic_pipeline():
    """Test the semantic enhanced pipeline with available patient data."""
    
    print("=" * 80)
    print("🧠 TESTING SEMANTIC ENHANCED PA AUTOMATION PIPELINE")
    print("=" * 80)
    
    # Setup paths
    pipeline_root = Path(__file__).parent
    input_dir = pipeline_root / "Input Data"
    output_dir = pipeline_root / "semantic_test_output"
    
    # Create output directory
    output_dir.mkdir(exist_ok=True)
    
    # Check available patients
    available_patients = []
    for patient_dir in input_dir.iterdir():
        if patient_dir.is_dir():
            pa_form = patient_dir / "PA.pdf"
            pa_form_alt = patient_dir / "pa.pdf"
            referral = patient_dir / "referral_package.pdf"
            
            if (pa_form.exists() or pa_form_alt.exists()) and referral.exists():
                available_patients.append(patient_dir.name)
    
    if not available_patients:
        print("❌ No valid patient folders found!")
        print(f"   Checked directory: {input_dir}")
        return False
    
    print(f"📁 Found {len(available_patients)} valid patient folders:")
    for patient in available_patients:
        print(f"   - {patient}")
    
    # Initialize pipeline
    try:
        print("\n🚀 Initializing Semantic Enhanced Pipeline...")
        pipeline = SemanticEnhancedPipeline()
        print("✅ Pipeline initialized successfully!")
    except Exception as e:
        print(f"❌ Failed to initialize pipeline: {e}")
        return False
    
    # Test with first available patient
    test_patient = available_patients[0]
    print(f"\n🧪 Testing with patient: {test_patient}")
    
    patient_dir = input_dir / test_patient
    
    try:
        # Process the patient
        result = pipeline.process_patient_folder(patient_dir, output_dir)
        
        if result["success"]:
            print(f"\n🎉 SUCCESS! Semantic processing completed for {test_patient}")
            
            # Display results summary
            report_data = result["report_data"]
            
            print(f"\n📊 PROCESSING SUMMARY:")
            print(f"   Patient: {report_data['patient_name']}")
            
            # Schema info
            schema_info = report_data["schema_info"]
            print(f"\n📋 Schema Analysis:")
            print(f"   Total fields: {schema_info['total_fields']}")
            print(f"   Categories: {schema_info['field_categories']}")
            
            # Extraction results
            extraction = report_data["extraction_results"]["summary"]
            print(f"\n🔍 Data Extraction:")
            print(f"   Total fields: {extraction.get('total_fields', 0)}")
            print(f"   Extracted: {extraction.get('extracted_fields', 0)}")
            print(f"   Success rate: {extraction.get('extraction_rate', '0%')}")
            
            # Semantic mapping results
            mapping = report_data["semantic_mapping"]["summary"]
            print(f"\n🧠 Semantic Mapping:")
            print(f"   Total mappings: {mapping.get('total_mappings', 0)}")
            print(f"   High confidence: {mapping.get('high_confidence', 0)}")
            print(f"   Medium confidence: {mapping.get('medium_confidence', 0)}")
            print(f"   Low confidence: {mapping.get('low_confidence', 0)}")
            
            # Value standardization results
            standardization = report_data["value_standardization"]["summary"]
            print(f"\n🔧 Value Standardization:")
            print(f"   Total fields: {standardization.get('total_fields', 0)}")
            print(f"   Transformed: {standardization.get('transformed_fields', 0)}")
            print(f"   Transformation rate: {standardization.get('transformation_rate', '0%')}")
            
            # Form filling results
            form_filling = report_data["form_filling"]
            print(f"\n📝 Form Filling:")
            print(f"   Status: {'✅ Success' if form_filling['success'] else '❌ Failed'}")
            print(f"   Output: {form_filling.get('output_path', 'N/A')}")
            
            # Show output files
            print(f"\n📄 Generated Files:")
            json_file = output_dir / f"{test_patient}_SEMANTIC_RESULTS.json"
            md_file = output_dir / f"{test_patient}_SEMANTIC_REPORT.md"
            pdf_file = result["filled_pdf_path"]
            
            print(f"   📊 Results JSON: {json_file}")
            print(f"   📋 Report MD: {md_file}")
            print(f"   📝 Filled PDF: {pdf_file}")
            
            # Show some transformation examples
            standardization_log = report_data["value_standardization"]["standardization_log"]
            if standardization_log:
                print(f"\n🔄 Value Transformations Applied:")
                for i, log_entry in enumerate(standardization_log[:5]):  # Show first 5
                    print(f"   {i+1}. {log_entry['field_id']}: '{log_entry['original_value']}' → '{log_entry['standardized_value']}'")
                if len(standardization_log) > 5:
                    print(f"   ... and {len(standardization_log) - 5} more transformations")
            
            return True
            
        else:
            print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_original():
    """Compare semantic enhanced results with original pipeline results."""
    
    print("\n" + "=" * 80)
    print("📊 COMPARING SEMANTIC ENHANCED VS ORIGINAL PIPELINE")
    print("=" * 80)
    
    # Check if we have results from both pipelines
    semantic_output = Path("semantic_test_output")
    original_output = Path("output")
    
    if not semantic_output.exists():
        print("❌ No semantic enhanced results found. Run test first.")
        return
    
    # Find common patients processed by both pipelines
    semantic_files = list(semantic_output.glob("*_SEMANTIC_RESULTS.json"))
    
    if not semantic_files:
        print("❌ No semantic results files found.")
        return
    
    print(f"📁 Found {len(semantic_files)} semantic enhanced results")
    
    # Analyze first result
    result_file = semantic_files[0]
    patient_name = result_file.stem.replace("_SEMANTIC_RESULTS", "")
    
    print(f"\n🔍 Analyzing results for: {patient_name}")
    
    try:
        with open(result_file, 'r') as f:
            semantic_data = json.load(f)
        
        # Display key improvements
        print(f"\n🚀 SEMANTIC ENHANCED FEATURES:")
        
        # Mapping analysis
        mapping_summary = semantic_data["semantic_mapping"]["summary"]
        print(f"   🧠 Semantic Mappings: {mapping_summary.get('total_mappings', 0)}")
        print(f"      - High confidence: {mapping_summary.get('high_confidence', 0)}")
        print(f"      - Medium confidence: {mapping_summary.get('medium_confidence', 0)}")
        
        # Standardization analysis
        std_summary = semantic_data["value_standardization"]["summary"]
        print(f"   🔧 Value Standardizations: {std_summary.get('transformed_fields', 0)}")
        print(f"      - Transformation rate: {std_summary.get('transformation_rate', '0%')}")
        
        # Show transformation examples
        std_log = semantic_data["value_standardization"]["standardization_log"]
        if std_log:
            print(f"\n   📝 Example Transformations:")
            for i, entry in enumerate(std_log[:3]):
                print(f"      {i+1}. {entry['transformation']}: '{entry['original_value']}' → '{entry['standardized_value']}'")
        
        print(f"\n✅ Semantic enhancement provides:")
        print(f"   - AI-powered field mapping between extracted data and PA forms")
        print(f"   - Automatic value standardization (dates, phones, names, etc.)")
        print(f"   - Confidence scoring for all mappings")
        print(f"   - Detailed transformation logging for debugging")
        print(f"   - Learning from corrections for continuous improvement")
        
    except Exception as e:
        print(f"❌ Error analyzing results: {e}")

def main():
    """Main test function."""
    
    print("🧪 Starting Semantic Enhanced Pipeline Test...")
    
    # Test the pipeline
    success = test_semantic_pipeline()
    
    if success:
        # Compare with original if available
        compare_with_original()
        
        print(f"\n🎉 SEMANTIC ENHANCED PIPELINE TEST COMPLETED SUCCESSFULLY!")
        print(f"\nKey improvements over original pipeline:")
        print(f"✅ AI-powered semantic field mapping")
        print(f"✅ Automatic value standardization")
        print(f"✅ Confidence scoring and validation")
        print(f"✅ Detailed transformation logging")
        print(f"✅ Learning capabilities for continuous improvement")
        
    else:
        print(f"\n❌ SEMANTIC ENHANCED PIPELINE TEST FAILED!")
        print(f"Check the logs above for error details.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
